import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:local_auth/local_auth.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import '../models/user_model.dart';
import '../models/auth_state.dart';

/// 🔐 Comprehensive Authentication Service for CHICA'S Chicken
/// Handles all authentication operations with security best practices
class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  // Firebase instances
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();
  final LocalAuthentication _localAuth = LocalAuthentication();
  
  // Secure storage for sensitive data
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // State management
  AuthState _authState = AuthState.initial();
  UserModel? _currentUser;
  StreamSubscription<User?>? _authStateSubscription;
  Timer? _sessionTimer;

  // Configuration
  static const Duration _sessionTimeout = Duration(minutes: 30);
  static const int _maxLoginAttempts = 5;
  static const Duration _lockoutDuration = Duration(minutes: 15);

  // Getters
  AuthState get authState => _authState;
  UserModel? get currentUser => _currentUser;
  bool get isAuthenticated => _authState.isAuthenticated;
  User? get firebaseUser => _auth.currentUser;

  /// Initialize the authentication service
  Future<void> initialize() async {
    try {
      _setAuthState(AuthState.loading());
      
      // Listen to Firebase auth state changes
      _authStateSubscription = _auth.authStateChanges().listen(_onAuthStateChanged);
      
      // Check for existing session
      await _checkExistingSession();
      
    } catch (e) {
      _setAuthState(AuthState.error(message: 'Failed to initialize authentication: $e'));
    }
  }

  /// Handle Firebase auth state changes
  Future<void> _onAuthStateChanged(User? user) async {
    if (user != null) {
      try {
        // Load user profile from Firestore
        final userDoc = await _firestore.collection('users').doc(user.uid).get();
        
        if (userDoc.exists) {
          _currentUser = UserModel.fromFirestore(userDoc);
          _setAuthState(AuthState.authenticated(
            userId: user.uid,
            email: user.email!,
            displayName: user.displayName,
            photoUrl: user.photoURL,
            emailVerified: user.emailVerified,
          ));
          
          // Update last login
          await _updateLastLogin();
          
          // Start session timer
          _startSessionTimer();
        } else {
          // User document doesn't exist, sign out
          await signOut();
        }
      } catch (e) {
        _setAuthState(AuthState.error(message: 'Failed to load user profile: $e'));
      }
    } else {
      _currentUser = null;
      _setAuthState(AuthState.unauthenticated());
      _stopSessionTimer();
    }
  }

  /// Check for existing session
  Future<void> _checkExistingSession() async {
    try {
      final lastActivity = await _secureStorage.read(key: 'last_activity');
      if (lastActivity != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final now = DateTime.now();
        
        if (now.difference(lastActivityTime) > _sessionTimeout) {
          // Session expired, sign out
          await signOut();
          return;
        }
      }
      
      // Check if user is already signed in
      final currentUser = _auth.currentUser;
      if (currentUser != null) {
        await _onAuthStateChanged(currentUser);
      } else {
        _setAuthState(AuthState.unauthenticated());
      }
    } catch (e) {
      _setAuthState(AuthState.error(message: 'Failed to check session: $e'));
    }
  }

  /// Sign up with email and password
  Future<AuthState> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    String? phone,
  }) async {
    try {
      _setAuthState(AuthState.loading());
      
      // Check if email is already in use
      final signInMethods = await _auth.fetchSignInMethodsForEmail(email);
      if (signInMethods.isNotEmpty) {
        final error = AuthState.error(
          message: 'An account with this email already exists',
          code: AuthErrorCodes.emailAlreadyInUse,
        );
        _setAuthState(error);
        return error;
      }

      // Create user account
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final user = credential.user!;
      
      // Update display name
      await user.updateDisplayName(name);
      
      // Send email verification
      await user.sendEmailVerification();
      
      // Create user profile in Firestore
      final userProfile = UserModel(
        uid: user.uid,
        email: email,
        name: name,
        phone: phone,
        emailVerified: false,
        addresses: [],
        preferences: const UserPreferences(
          favoriteItems: [],
          dietaryRestrictions: [],
          pushNotifications: true,
          emailNotifications: true,
          smsNotifications: false,
          preferredLanguage: 'en',
          biometricAuth: false,
          theme: 'system',
        ),
        loyaltyInfo: const LoyaltyInfo(
          points: 100, // Welcome bonus
          tier: 'Bronze',
          totalSpent: 0,
          totalOrders: 0,
          availableRewards: [],
        ),
        createdAt: DateTime.now(),
        lastLogin: DateTime.now(),
      );

      await _firestore.collection('users').doc(user.uid).set(userProfile.toJson());
      
      // Award signup bonus points
      await _awardSignupBonus(user.uid);
      
      return _authState;
    } on FirebaseAuthException catch (e) {
      final error = _handleFirebaseAuthError(e);
      _setAuthState(error);
      return error;
    } catch (e) {
      final error = AuthState.error(message: 'Sign up failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign in with email and password
  Future<AuthState> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      _setAuthState(AuthState.loading());
      
      // Check login attempts
      if (await _isAccountLocked(email)) {
        final error = AuthState.error(
          message: 'Account temporarily locked due to too many failed attempts',
          code: AuthErrorCodes.tooManyRequests,
        );
        _setAuthState(error);
        return error;
      }

      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      // Clear failed login attempts on success
      await _clearFailedLoginAttempts(email);
      
      return _authState;
    } on FirebaseAuthException catch (e) {
      // Track failed login attempt
      await _trackFailedLoginAttempt(email);
      
      final error = _handleFirebaseAuthError(e);
      _setAuthState(error);
      return error;
    } catch (e) {
      final error = AuthState.error(message: 'Sign in failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign in with Google
  Future<AuthState> signInWithGoogle() async {
    try {
      _setAuthState(AuthState.loading());
      
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      if (googleUser == null) {
        _setAuthState(AuthState.unauthenticated());
        return _authState;
      }

      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      final userCredential = await _auth.signInWithCredential(credential);
      final user = userCredential.user!;
      
      // Check if this is a new user
      if (userCredential.additionalUserInfo?.isNewUser == true) {
        await _createUserProfile(user);
        await _awardSignupBonus(user.uid);
      }
      
      return _authState;
    } catch (e) {
      final error = AuthState.error(message: 'Google sign in failed: $e');
      _setAuthState(error);
      return error;
    }
  }

  /// Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _googleSignIn.signOut();
      await _secureStorage.deleteAll();
      _stopSessionTimer();
    } catch (e) {
      debugPrint('Sign out error: $e');
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return true;
    } catch (e) {
      debugPrint('Password reset error: $e');
      return false;
    }
  }

  /// Update user profile
  Future<bool> updateUserProfile(UserModel updatedUser) async {
    try {
      await _firestore.collection('users').doc(updatedUser.uid).update(
        updatedUser.copyWith(updatedAt: DateTime.now()).toJson(),
      );
      _currentUser = updatedUser;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Profile update error: $e');
      return false;
    }
  }

  /// Check if biometric authentication is available
  Future<bool> isBiometricAvailable() async {
    try {
      return await _localAuth.canCheckBiometrics;
    } catch (e) {
      return false;
    }
  }

  /// Authenticate with biometrics
  Future<BiometricAuthResult> authenticateWithBiometrics() async {
    try {
      final isAvailable = await _localAuth.canCheckBiometrics;
      if (!isAvailable) {
        return BiometricAuthResult.failure(
          message: 'Biometric authentication not available',
          errorType: BiometricAuthError.notAvailable,
        );
      }

      final result = await _localAuth.authenticate(
        localizedReason: 'Authenticate to access your CHICA\'S Chicken account',
        options: const AuthenticationOptions(
          biometricOnly: true,
          stickyAuth: true,
        ),
      );

      if (result) {
        return BiometricAuthResult.success();
      } else {
        return BiometricAuthResult.failure(
          message: 'Biometric authentication cancelled',
          errorType: BiometricAuthError.cancelled,
        );
      }
    } on PlatformException catch (e) {
      return BiometricAuthResult.failure(
        message: e.message ?? 'Biometric authentication failed',
        errorType: BiometricAuthError.unknown,
      );
    }
  }

  /// Private helper methods
  void _setAuthState(AuthState newState) {
    _authState = newState;
    notifyListeners();
  }

  AuthState _handleFirebaseAuthError(FirebaseAuthException e) {
    String message;
    switch (e.code) {
      case 'user-not-found':
        message = 'No account found with this email address';
        break;
      case 'wrong-password':
        message = 'Incorrect password';
        break;
      case 'email-already-in-use':
        message = 'An account with this email already exists';
        break;
      case 'weak-password':
        message = 'Password is too weak';
        break;
      case 'invalid-email':
        message = 'Invalid email address';
        break;
      case 'too-many-requests':
        message = 'Too many failed attempts. Please try again later';
        break;
      case 'network-request-failed':
        message = 'Network error. Please check your connection';
        break;
      default:
        message = e.message ?? 'Authentication failed';
    }
    
    return AuthState.error(message: message, code: e.code);
  }

  Future<void> _createUserProfile(User user) async {
    final userProfile = UserModel(
      uid: user.uid,
      email: user.email!,
      name: user.displayName ?? 'User',
      profileImageUrl: user.photoURL,
      emailVerified: user.emailVerified,
      addresses: [],
      preferences: const UserPreferences(
        favoriteItems: [],
        dietaryRestrictions: [],
        pushNotifications: true,
        emailNotifications: true,
        smsNotifications: false,
        preferredLanguage: 'en',
        biometricAuth: false,
        theme: 'system',
      ),
      loyaltyInfo: const LoyaltyInfo(
        points: 100, // Welcome bonus
        tier: 'Bronze',
        totalSpent: 0,
        totalOrders: 0,
        availableRewards: [],
      ),
      createdAt: DateTime.now(),
      lastLogin: DateTime.now(),
    );

    await _firestore.collection('users').doc(user.uid).set(userProfile.toJson());
  }

  Future<void> _awardSignupBonus(String userId) async {
    try {
      // This would integrate with your loyalty service
      debugPrint('Awarding signup bonus to user: $userId');
    } catch (e) {
      debugPrint('Failed to award signup bonus: $e');
    }
  }

  Future<void> _updateLastLogin() async {
    if (_currentUser != null) {
      await _firestore.collection('users').doc(_currentUser!.uid).update({
        'lastLogin': Timestamp.now(),
      });
      
      await _secureStorage.write(
        key: 'last_activity',
        value: DateTime.now().toIso8601String(),
      );
    }
  }

  void _startSessionTimer() {
    _stopSessionTimer();
    _sessionTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      _checkSessionTimeout();
    });
  }

  void _stopSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = null;
  }

  Future<void> _checkSessionTimeout() async {
    try {
      final lastActivity = await _secureStorage.read(key: 'last_activity');
      if (lastActivity != null) {
        final lastActivityTime = DateTime.parse(lastActivity);
        final now = DateTime.now();
        
        if (now.difference(lastActivityTime) > _sessionTimeout) {
          await signOut();
        }
      }
    } catch (e) {
      debugPrint('Session timeout check error: $e');
    }
  }

  Future<bool> _isAccountLocked(String email) async {
    try {
      final attempts = await _secureStorage.read(key: 'failed_attempts_$email');
      final lockTime = await _secureStorage.read(key: 'lock_time_$email');
      
      if (attempts != null && lockTime != null) {
        final attemptCount = int.parse(attempts);
        final lockDateTime = DateTime.parse(lockTime);
        
        if (attemptCount >= _maxLoginAttempts) {
          final now = DateTime.now();
          if (now.difference(lockDateTime) < _lockoutDuration) {
            return true;
          } else {
            // Lockout period expired, clear attempts
            await _clearFailedLoginAttempts(email);
          }
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> _trackFailedLoginAttempt(String email) async {
    try {
      final attempts = await _secureStorage.read(key: 'failed_attempts_$email');
      final attemptCount = attempts != null ? int.parse(attempts) + 1 : 1;
      
      await _secureStorage.write(key: 'failed_attempts_$email', value: attemptCount.toString());
      
      if (attemptCount >= _maxLoginAttempts) {
        await _secureStorage.write(key: 'lock_time_$email', value: DateTime.now().toIso8601String());
      }
    } catch (e) {
      debugPrint('Failed to track login attempt: $e');
    }
  }

  Future<void> _clearFailedLoginAttempts(String email) async {
    try {
      await _secureStorage.delete(key: 'failed_attempts_$email');
      await _secureStorage.delete(key: 'lock_time_$email');
    } catch (e) {
      debugPrint('Failed to clear login attempts: $e');
    }
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _stopSessionTimer();
    super.dispose();
  }
}

// 🧪 QSR App Widget Tests
// Tests for Chica's Chicken ordering app

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:qsr_app/main.dart';
import 'package:qsr_app/services/theme_service.dart';
import 'package:qsr_app/services/language_service.dart';
import 'package:qsr_app/services/mock_auth_service.dart';

void main() {
  testWidgets('QSR App launches successfully', (WidgetTester tester) async {
    // Create services for testing
    final themeService = ThemeService();
    final languageService = LanguageService();
    final authService = MockAuthService();

    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp(
      themeService: themeService,
      languageService: languageService,
      authService: authService,
    ));

    // Wait for the app to settle
    await tester.pumpAndSettle();

    // Verify that the app launches without crashing
    // Look for key elements that should be present
    expect(find.byType(MaterialApp), findsOneWidget);

    // The app should have some navigation or content
    // This is a basic smoke test to ensure the app doesn't crash on startup
  });
}

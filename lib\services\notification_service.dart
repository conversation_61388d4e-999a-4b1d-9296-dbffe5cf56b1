// 🔔 Flutter Notification Service
// Handles push notifications, local notifications, WebSocket connections, and real-time updates
// Now includes timed feedback notifications with deep linking!

import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart'; // Re-enabled for notifications
import 'package:firebase_messaging/firebase_messaging.dart'; // Re-enabled for notifications
import 'package:flutter_local_notifications/flutter_local_notifications.dart'; // NEW: Local notifications
import 'package:timezone/timezone.dart' as tz; // NEW: For scheduling notifications
import 'package:timezone/data/latest.dart' as tz; // NEW: Timezone data
import 'package:shared_preferences/shared_preferences.dart'; // NEW: User preferences
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:web_socket_channel/status.dart' as status;
import '../utils/logger.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Firebase Messaging instance (now enabled for notifications!)
  FirebaseMessaging? _messaging;

  // NEW: Local notifications for timed feedback reminders
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  // WebSocket connection
  WebSocketChannel? _channel;
  StreamSubscription? _wsSubscription;

  // Notification streams
  final StreamController<Map<String, dynamic>> _notificationController =
      StreamController<Map<String, dynamic>>.broadcast();

  final StreamController<Map<String, dynamic>> _orderUpdateController =
      StreamController<Map<String, dynamic>>.broadcast();

  // NEW: Stream for notification taps (for deep linking)
  final StreamController<String> _notificationTapController =
      StreamController<String>.broadcast();

  // Public streams
  Stream<Map<String, dynamic>> get notificationStream => _notificationController.stream;
  Stream<Map<String, dynamic>> get orderUpdateStream => _orderUpdateController.stream;
  Stream<String> get onNotificationTap => _notificationTapController.stream; // NEW

  // Current user ID
  String? _currentUserId;

  // 🚀 Initialize notification service
  Future<void> initialize({String? userId}) async {
    try {
      _currentUserId = userId ?? 'guest_${DateTime.now().millisecondsSinceEpoch}';

      // NEW: Initialize timezone data for scheduled notifications
      tz.initializeTimeZones();

      // NEW: Initialize local notifications first
      await _initializeLocalNotifications();

      // Initialize Firebase (now enabled for notifications!)
      await _initializeFirebase();

      // Initialize WebSocket connection
      await _initializeWebSocket();

      // NEW: Schedule daily feedback notification
      await scheduleDailyFeedbackNotification();

      AppLogger.info('Notification service initialized for user: $_currentUserId');
    } catch (error) {
      AppLogger.error('Failed to initialize notification service: $error');
    }
  }

  // 📱 Initialize local notifications
  Future<void> _initializeLocalNotifications() async {
    try {
      // Settings for Android notifications
      const AndroidInitializationSettings androidSettings =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // Settings for iOS notifications
      const DarwinInitializationSettings iosSettings =
          DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      // Combine all settings
      const InitializationSettings initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      // Initialize with our settings and tell it what to do when someone taps a notification
      await _localNotifications.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      AppLogger.info('Local notifications initialized successfully');
    } catch (error) {
      AppLogger.error('Failed to initialize local notifications: $error');
    }
  }

  // 🔥 Initialize Firebase messaging (now enabled for notifications!)
  Future<void> _initializeFirebase() async {
    try {
      // Skip Firebase on web for now
      if (kIsWeb) {
        AppLogger.info('Skipping Firebase on web platform');
        return;
      }

      // Check if Firebase is available
      if (Firebase.apps.isEmpty) {
        AppLogger.warning('Firebase not initialized, skipping FCM setup');
        return;
      }

      _messaging = FirebaseMessaging.instance;

      // Request permission for notifications
      NotificationSettings settings = await _messaging!.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        AppLogger.info('User granted permission for notifications');

        // Get FCM token
        String? token = await _messaging!.getToken();
        if (token != null) {
          AppLogger.info('FCM Token: $token');
          // TODO: Send token to your backend server
        }

        // Listen for token refresh
        _messaging!.onTokenRefresh.listen((token) {
          AppLogger.info('FCM Token refreshed: $token');
          // TODO: Send new token to your backend server
        });

        // Handle foreground messages
        FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

        // Handle messages when app is opened from notification
        FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

      } else {
        AppLogger.warning('User declined or has not accepted notification permissions');
      }

    } catch (error) {
      AppLogger.error('Failed to initialize Firebase messaging: $error');
    }
  }

  // 🌐 Initialize WebSocket connection (DISABLED FOR DEVELOPMENT)
  Future<void> _initializeWebSocket() async {
    // 🚀 Performance: WebSocket disabled to prevent crashes during development
    AppLogger.info('WebSocket connection disabled for development');
    return;

    /* Original WebSocket code commented out to prevent crashes
    try {
      final wsUrl = ApiConfig.baseUrl.replaceFirst('http', 'ws');
      _channel = WebSocketChannel.connect(Uri.parse(wsUrl));

      // Register user with WebSocket server
      _channel!.sink.add(jsonEncode({
        'type': 'register',
        'userId': _currentUserId,
      }));

      // Listen for WebSocket messages
      _wsSubscription = _channel!.stream.listen(
        _handleWebSocketMessage,
        onError: _handleWebSocketError,
        onDone: _handleWebSocketClosed,
      );

      AppLogger.info('WebSocket connection established');
    } catch (error) {
      AppLogger.error('Failed to initialize WebSocket: $error');
    }
    */
  }



  // 🎯 Handle notification tap
  void _onNotificationTapped(NotificationResponse response) {
    AppLogger.info('Notification tapped: ${response.payload}');

    // Send the payload (route) to whoever is listening
    if (response.payload != null) {
      _notificationTapController.add(response.payload!);
    }
  }

  // 📨 Handle foreground messages from Firebase
  void _handleForegroundMessage(RemoteMessage message) {
    AppLogger.info('Received foreground message: ${message.messageId}');

    final notification = {
      'id': message.messageId ?? '',
      'title': message.notification?.title ?? '',
      'body': message.notification?.body ?? '',
      'data': message.data,
      'timestamp': DateTime.now().toIso8601String(),
      'type': 'push',
    };

    _notificationController.add(notification);

    // Show a local notification so the user can see it
    if (message.notification != null) {
      _showLocalNotification(
        title: message.notification!.title ?? 'Chica\'s Chicken',
        body: message.notification!.body ?? 'You have a new message!',
        payload: message.data['route'] ?? '/loyalty',
      );
    }

    // Check if it's an order update
    if (message.data['type'] == 'order_status') {
      _orderUpdateController.add({
        'orderId': message.data['orderId'],
        'status': message.data['status'],
        'title': message.notification?.title,
        'body': message.notification?.body,
        'timestamp': DateTime.now().toIso8601String(),
      });
    }
  }

  // 🎯 Handle notification tap from Firebase
  void _handleNotificationTap(RemoteMessage message) {
    AppLogger.info('Firebase notification tapped: ${message.data}');

    // Navigate to the specified route
    String route = message.data['route'] ?? '/loyalty';
    _notificationTapController.add(route);
  }



  // 📅 Schedule daily feedback notification
  // This is the main feature! It schedules a notification every day at 6 PM
  Future<void> scheduleDailyFeedbackNotification() async {
    try {
      // Check if user has enabled notifications
      final prefs = await SharedPreferences.getInstance();
      bool notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;

      if (!notificationsEnabled) {
        AppLogger.info('Notifications disabled by user');
        return;
      }

      // Cancel any existing scheduled notification
      await _localNotifications.cancel(1);

      // Create the notification details
      const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
        'daily_feedback', // Channel ID
        'Daily Feedback Reminders', // Channel name
        channelDescription: 'Daily reminders to share feedback and earn rewards',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
      );

      const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Schedule for 6 PM today (or tomorrow if it's already past 6 PM)
      final now = DateTime.now();
      DateTime scheduledTime = DateTime(now.year, now.month, now.day, 18, 0); // 6 PM

      // If it's already past 6 PM today, schedule for tomorrow
      if (scheduledTime.isBefore(now)) {
        scheduledTime = scheduledTime.add(const Duration(days: 1));
      }

      // Convert to timezone-aware datetime
      final tz.TZDateTime tzScheduledTime = tz.TZDateTime.from(scheduledTime, tz.local);

      // Schedule the notification
      await _localNotifications.zonedSchedule(
        1, // Notification ID
        '🌟 Loved your meal?', // Title
        'Share your feedback and earn rewards! Tap to visit your loyalty page.', // Body
        tzScheduledTime,
        notificationDetails,
        payload: '/loyalty', // This tells the app where to go when tapped
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
        matchDateTimeComponents: DateTimeComponents.time, // Repeat daily at the same time
      );

      AppLogger.info('Daily feedback notification scheduled for ${scheduledTime.toString()}');
    } catch (error) {
      AppLogger.error('Failed to schedule daily notification: $error');
    }
  }

  // 📨 Show a local notification immediately
  Future<void> _showLocalNotification({
    required String title,
    required String body,
    String? payload,
  }) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      'instant_notifications',
      'Instant Notifications',
      channelDescription: 'Immediate notifications',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000, // Unique ID
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  // 🧪 Send test notification (for testing purposes)
  Future<void> sendTestNotification() async {
    await _showLocalNotification(
      title: '🧪 Test Notification',
      body: 'This is a test notification from Chica\'s Chicken!',
      payload: '/loyalty',
    );
    AppLogger.info('Test notification sent');
  }

  // ⚙️ Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('notifications_enabled') ?? true;
  }

  // ⚙️ Enable or disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications_enabled', enabled);

    if (enabled) {
      // Re-schedule daily notification
      await scheduleDailyFeedbackNotification();
      AppLogger.info('Notifications enabled');
    } else {
      // Cancel all scheduled notifications
      await _localNotifications.cancelAll();
      AppLogger.info('Notifications disabled');
    }
  }

  // 🧹 Dispose resources
  void dispose() {
    _wsSubscription?.cancel();
    _channel?.sink.close(status.goingAway);
    _notificationController.close();
    _orderUpdateController.close();
    _notificationTapController.close(); // NEW: Close notification tap stream
  }

  // 📊 Get connection status
  bool get isWebSocketConnected => _channel != null;
  bool get isFirebaseInitialized => false; // Temporarily disabled for web
}

// 🔔 Background message handler (temporarily disabled for web)
// @pragma('vm:entry-point')
// Future<void> _handleBackgroundMessage(RemoteMessage message) async {
//   AppLogger.info('Handling background message: ${message.messageId}');
//   // Handle background notification logic here
// }

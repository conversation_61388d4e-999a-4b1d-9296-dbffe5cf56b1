import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../screens/coming_soon_screen.dart';
import '../screens/favorites_screen.dart';
import '../services/language_service.dart';
import '../widgets/accessibility_widgets.dart';
import '../screens/settings_screen.dart';
import '../screens/support_screen.dart';
import '../screens/terms_of_use_screen.dart';
import '../screens/privacy_policy_screen.dart';
import '../screens/login_screen.dart';
import '../screens/signup_screen.dart';
import '../screens/account_screen.dart';
import '../widgets/auth_required_wrapper.dart';


class NavigationMenuDrawer extends StatelessWidget {
  const NavigationMenuDrawer({Key? key}) : super(key: key);

  void _navigateToComingSoon(BuildContext context, String featureName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ComingSoonScreen(featureName: featureName),
      ),
    );
  }

  void _navigateToTermsOfUse(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TermsOfUseScreen(),
      ),
    );
  }

  void _navigateToPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PrivacyPolicyScreen(),
      ),
    );
  }

  void _navigateToSupport(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SupportScreen(),
      ),
    );
  }

  void _navigateToLogin(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const LoginScreen(),
      ),
    );
  }

  void _navigateToSignup(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SignupScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            return ListView(
              padding: EdgeInsets.zero,
              children: [
                // Header with close button
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'NAVIGATION MENU',
                        style: TextStyle(
                          fontFamily: 'SofiaRoughBlackThree',
                          fontSize: 25,
                          fontWeight: FontWeight.bold,
                          color: Colors.brown,
                        ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close),
                        onPressed: () => Navigator.pop(context),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),

                // Chica's Rewards Section
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Icon(Icons.star, color: Colors.deepOrange.shade400),
                          const SizedBox(width: 8),
                          const Text(
                            "CHICA'S Rewards",
                            style: TextStyle(
                              fontFamily: 'SofiaRoughBlackThree',
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Save your order history and information for a faster checkout.',
                        style: TextStyle(color: Colors.grey),
                      ),
                      const SizedBox(height: 16),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth - 32,
                        ),
                        child: ElevatedButton(
                          onPressed: () => _navigateToLogin(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepOrange,
                            minimumSize: const Size.fromHeight(45),
                          ),
                          child: const Text('Log In'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      ConstrainedBox(
                        constraints: BoxConstraints(
                          maxWidth: constraints.maxWidth - 32,
                        ),
                        child: OutlinedButton(
                          onPressed: () => _navigateToSignup(context),
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(color: Colors.deepOrange),
                            minimumSize: const Size.fromHeight(45),
                          ),
                          child: const Text(
                            'Sign Up',
                            style: TextStyle(color: Colors.deepOrange),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 1),

                // Language Selection
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child: Consumer<LanguageService>(
                          builder: (context, languageService, child) {
                            return TextButton(
                              onPressed: () {
                                languageService.setLanguage(Language.french);
                                Navigator.pop(context); // Close drawer after selection
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                backgroundColor: languageService.language == Language.french
                                    ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                    : Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(
                                    color: languageService.language == Language.french
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.transparent,
                                  ),
                                ),
                              ),
                              child: AccessibleText(
                                languageService.getLanguageDisplayName(Language.french),
                                style: TextStyle(
                                  color: languageService.language == Language.french
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.grey,
                                  fontWeight: languageService.language == Language.french
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Consumer<LanguageService>(
                          builder: (context, languageService, child) {
                            return TextButton(
                              onPressed: () {
                                languageService.setLanguage(Language.english);
                                Navigator.pop(context); // Close drawer after selection
                              },
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                backgroundColor: languageService.language == Language.english
                                    ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                                    : Colors.transparent,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                  side: BorderSide(
                                    color: languageService.language == Language.english
                                        ? Theme.of(context).colorScheme.primary
                                        : Colors.transparent,
                                  ),
                                ),
                              ),
                              child: AccessibleText(
                                languageService.getLanguageDisplayName(Language.english),
                                style: TextStyle(
                                  color: languageService.language == Language.english
                                      ? Theme.of(context).colorScheme.primary
                                      : Colors.grey,
                                  fontWeight: languageService.language == Language.english
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),

                // Menu Items - Using constrained height to prevent overflow
                ...ListTile.divideTiles(
                  context: context,
                  tiles: [
                    ListTile(
                      leading: const Icon(Icons.person_outline),
                      title: const AccessibleText('My Account'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AuthRequiredWrapper(
                              featureName: 'My Account',
                              message: 'Sign in to view your account details, order history, and manage your preferences.',
                              child: AccountScreen(),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.favorite_outline),
                      title: const AccessibleText('Favorites'),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.pink,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const AccessibleText(
                          'NEW',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AuthRequiredWrapper(
                              featureName: 'Favorites',
                              message: 'Sign in to save and view your favorite menu items.',
                              child: FavoritesScreen(),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.local_fire_department),
                      title: const AccessibleText('Special Offers'),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.deepOrange,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const AccessibleText(
                          'NEW',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                      onTap: () => _navigateToComingSoon(context, 'Special Offers'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.location_on_outlined),
                      title: const AccessibleText('Find a Restaurant'),
                      onTap: () => _navigateToComingSoon(context, 'Restaurant Locator'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.card_giftcard),
                      title: const AccessibleText('Rewards Program'),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const AuthRequiredWrapper(
                              featureName: 'Rewards Program',
                              message: 'Sign in to view your reward points, tier status, and available rewards.',
                              child: ComingSoonScreen(featureName: 'Rewards Program'),
                            ),
                          ),
                        );
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.local_offer_outlined),
                      title: const AccessibleText('Catering'),
                      onTap: () => _navigateToComingSoon(context, 'Catering'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.card_giftcard),
                      title: const AccessibleText('Gift Cards'),
                      onTap: () => _navigateToComingSoon(context, 'Gift Cards'),
                    ),
                    ListTile(
                      leading: const Icon(Icons.help_outline),
                      title: const AccessibleText('Support & FAQ'),
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToSupport(context);
                      },
                    ),
                    ListTile(
                      leading: const Icon(Icons.settings_outlined),
                      title: const AccessibleText('Settings'),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const AccessibleText(
                          'NEW',
                          style: TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const SettingsScreen(),
                          ),
                        );
                      },
                    ),
                    // 🚀 Developer features removed for production
                  ],
                ),

                const SizedBox(height: 16),

                // Legal Terms & Policies Section
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      AccessibleText(
                        'LEGAL TERMS & POLICIES',
                        style: const TextStyle(
                          color: Colors.brown,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      InkWell(
                        onTap: () => _navigateToTermsOfUse(context),
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: AccessibleText('Terms of Use'),
                        ),
                      ),
                      InkWell(
                        onTap: () => _navigateToPrivacyPolicy(context),
                        child: const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8.0),
                          child: AccessibleText('Privacy Policy'),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            );
          },
        ),
      ),
    );
  }

}

name: qsr_app
description: A new Flutter project for QSR ordering.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.2
  http: ^1.4.0 # For making API calls to backend
  provider: ^6.0.0 # For managing the app's state
  flutter_animate: ^4.5.0 # For GSAP-like animations
  flutter_staggered_animations: ^1.1.1 # For staggered list animations
  visibility_detector: ^0.4.0+2 # For scroll-triggered animations
  animations: ^2.0.11 # For page transitions

  # Backend Integration (Firebase - now enabled for notifications)
  firebase_core: ^2.24.2 # Firebase core functionality
  firebase_auth: ^4.15.3 # Firebase authentication
  cloud_firestore: ^4.13.6 # Firestore database
  firebase_analytics: ^10.7.4 # Analytics tracking
  firebase_messaging: ^14.7.10 # Push notifications - NOW ENABLED!

  # Local Notifications (for testing and offline notifications)
  flutter_local_notifications: ^17.2.2 # Local notifications
  timezone: ^0.9.2 # For scheduling notifications at specific times

  # Deep Linking (to open specific pages from notifications)
  go_router: ^14.2.7 # Modern navigation with deep linking support

  # Authentication & Security (Web-compatible versions)
  # local_auth: ^2.1.7 # Biometric authentication (mobile only)
  # google_sign_in: ^6.1.6 # Google Sign-In (temporarily disabled)
  # sign_in_with_apple: ^5.0.0 # Apple Sign-In (iOS only)

  # Payment Processing (Stripe temporarily disabled for testing)
  # flutter_stripe: ^9.5.0 # Stripe payments

  # Additional utilities
  shared_preferences: ^2.2.2 # Local storage
  connectivity_plus: ^5.0.2 # Network connectivity
  dio: ^5.4.0 # Advanced HTTP client
  web_socket_channel: ^2.4.0 # WebSocket support for real-time notifications

  # Game Integration (temporarily disabled for dependency resolution)
  # flutter_inappwebview: ^5.8.0 # Advanced WebView with better game support
  # permission_handler: ^11.1.0 # For game permissions (camera, microphone, etc.)
  # wakelock_plus: ^1.2.5 # Keep screen awake during games

  # Revel Systems API Integration
  json_annotation: ^4.8.1 # JSON serialization
  crypto: ^3.0.3 # For API signature generation
  uuid: ^4.2.1 # For unique request IDs
  flutter_secure_storage: ^9.0.0 # Secure token storage
  url_launcher: ^6.2.2 # For payment redirects

  # Offline storage
  hive: ^2.2.3 # NoSQL local database
  hive_flutter: ^1.1.0 # Hive Flutter integration
  sqflite: ^2.3.0 # SQLite local database
  path: ^1.8.3 # Path utilities
  cached_network_image: ^3.3.0 # For image caching
  flutter_cache_manager: ^3.3.1 # Cache management
  image: ^4.1.3 # Image processing

  # Crash Reporting & Analytics (temporarily disabled for dependency resolution)
  # sentry_flutter: ^7.14.0
  # device_info_plus: ^9.1.1
  # package_info_plus: ^4.2.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter

  flutter_lints: ^2.0.0

  # Testing & QA
  mockito: ^5.4.4
  build_runner: ^2.4.7
  fake_async: ^1.3.1
  flutter_driver:
    sdk: flutter
  http_mock_adapter: ^0.6.1
  golden_toolkit: ^0.15.0

  # Code generation
  hive_generator: ^2.0.1
  json_serializable: ^6.7.1 # JSON code generation for Revel API models

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/images/
    - assets/CC-Penta-3.png
    - assets/games/
    - assets/games/chicken_catch/

  fonts:
    - family: SofiaRoughBlackThree
      fonts:
        - asset: assets/fonts/SofiaRoughBlackThree.ttf
          weight: 900
    
    - family: MontserratBlack
      fonts:
        - asset: assets/fonts/Montserrat-Black.ttf
          weight: 900
        - asset: assets/fonts/Montserrat-BlackItalic.ttf
          weight: 900
          style: italic
    
    - family: SofiaSans
      fonts:
        - asset: assets/fonts/SofiaSans-Regular.ttf
          weight: 400
        - asset: assets/fonts/SofiaSans-Medium.ttf
          weight: 500
        - asset: assets/fonts/SofiaSans-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/SofiaSans-Bold.ttf
          weight: 700
        - asset: assets/fonts/SofiaSans-ExtraBold.ttf
          weight: 800
        - asset: assets/fonts/SofiaSans-Black.ttf
          weight: 900

import 'package:flutter/material.dart';
import '../config/delivery_config.dart';
import '../screens/delivery/city_selection_screen.dart';
import '../services/cart_service.dart';

/// 🍽️ Order Type Selection Screen
/// First screen users see - choose between pickup or delivery
class OrderTypeSelectionScreen extends StatefulWidget {
  final CartService? cartService;

  const OrderTypeSelectionScreen({super.key, this.cartService});

  @override
  State<OrderTypeSelectionScreen> createState() => _OrderTypeSelectionScreenState();
}

class _OrderTypeSelectionScreenState extends State<OrderTypeSelectionScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Hover states for cards
  bool _isPickupHovered = false;
  bool _isPickupPressed = false;
  bool _isDeliveryHovered = false;
  bool _isDeliveryPressed = false;

  /// Build the logo header widget
  Widget _buildLogoHeader() {
    final screenWidth = MediaQuery.of(context).size.width;
    final logoWidth = (screenWidth * 0.45).clamp(160.0, 320.0);
    return Image.asset(
      'assets/images/CHICAS-CHICKEN-Logo.png',
      width: logoWidth,
      fit: BoxFit.contain,
      semanticLabel: 'Chicas Chicken Logo',
    );
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 🏪 Handle pickup selection - now goes to city selection like delivery
  void _selectPickup() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CitySelectionScreen(isPickup: true),
      ),
    );
  }

  /// 🚚 Handle delivery selection
  void _selectDelivery() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const CitySelectionScreen(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo header
                _buildLogoHeader(),
                const SizedBox(height: 24),
                _buildTitleText(Theme.of(context).brightness == Brightness.dark),
                const SizedBox(height: 32),
                _buildOrderTypeOptions(),
                const SizedBox(height: 80), // Extra scrollable area at the bottom
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build the title text widget
  Widget _buildTitleText(bool isDark) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 55).clamp(280.0, 320.0);
    // Spiced red: #B22222 (Firebrick), #D7263D (Spiced Red), #8B1A1A (Dark Red)
    return Container(
      width: cardWidth,
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 14),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFD7263D), // Spiced Red
            Color(0xFFB22222), // Firebrick
            Color(0xFF8B1A1A), // Dark Red
          ],
        ),
        borderRadius: BorderRadius.circular(10),
        border: Border.all(
          color: Colors.black.withOpacity(0.12),
          width: 1,
        ),
        boxShadow: const [
          BoxShadow(
            color: Color.fromRGBO(255, 164, 176, 1),
            blurRadius: 20,
            offset: Offset(0, 1),
            spreadRadius: 0,
          ),
        ],
      ),
      child: const Center(
        child: Text(
          'YOU COMING OR WE SENDING?',
          style: TextStyle(
            fontSize: 20,
            color: Colors.white,
            fontWeight: FontWeight.w900,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }

  /// 🍽️ Build order type options with consistent card sizes and animations
  Widget _buildOrderTypeOptions() {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = (screenWidth - 55).clamp(280.0, 320.0);
    const cardMinHeight = 180.0;
    const cardMaxHeight = 220.0;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(-0.5, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          )),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: cardWidth,
                  maxWidth: cardWidth,
                  minHeight: cardMinHeight,
                  maxHeight: cardMaxHeight,
                ),
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: _buildPickupOption(),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(height: 32),
        SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0.5, 0),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: _animationController,
            curve: const Interval(0.2, 1.0, curve: Curves.easeOutBack),
          )),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Center(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minWidth: cardWidth,
                  maxWidth: cardWidth,
                  minHeight: cardMinHeight,
                  maxHeight: cardMaxHeight,
                ),
                child: SingleChildScrollView(
                  physics: const NeverScrollableScrollPhysics(),
                  child: _buildDeliveryOption(),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 🏪 Build pickup option - always accessible
  Widget _buildPickupOption() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final isAvailable = DeliveryConfig.isPickupAvailable;

    return MouseRegion(
      onEnter: (_) => setState(() => _isPickupHovered = true),
      onExit: (_) => setState(() => _isPickupHovered = false),
      child: GestureDetector(
        onTapDown: (_) => setState(() => _isPickupPressed = true),
        onTapUp: (_) => setState(() => _isPickupPressed = false),
        onTapCancel: () => setState(() => _isPickupPressed = false),
        onTap: _selectPickup,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(_isPickupPressed ? 0.95 : (_isPickupHovered ? 1.02 : 1.0)),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.green.shade700,
                Colors.green.shade800,
                Colors.green.shade900,
              ],
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFF145A32).withOpacity(0.45),
                blurRadius: _isPickupHovered ? 25 : 20,
                offset: Offset(0, _isPickupHovered ? 15 : 10),
                spreadRadius: _isPickupHovered ? 4 : 2,
              ),
              if (!isDark)
                BoxShadow(
                  color: const Color(0xFF145A32).withOpacity(0.18),
                  blurRadius: _isPickupHovered ? 20 : 15,
                  offset: Offset(0, _isPickupHovered ? 8 : 5),
                ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.center,
                child: SizedBox(
                  height: 80,
                  width: 80,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.store_outlined,
                        size: 70,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 6),
              const Text(
                'PICKUP',
                style: TextStyle(
                  fontSize: 35,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 1.5,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  isAvailable ? 'READY IN 20-25 MIN' : 'BROWSE MENU',
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 🚚 Build delivery option
  Widget _buildDeliveryOption() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final availableCities = DeliveryConfig.getAvailableCitiesOnly();

    return MouseRegion(
      onEnter: (_) => setState(() => _isDeliveryHovered = true),
      onExit: (_) => setState(() => _isDeliveryHovered = false),
      child: GestureDetector(
        onTapDown: (_) => setState(() => _isDeliveryPressed = true),
        onTapUp: (_) => setState(() => _isDeliveryPressed = false),
        onTapCancel: () => setState(() => _isDeliveryPressed = false),
        onTap: _selectDelivery,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          transform: Matrix4.identity()
            ..scale(_isDeliveryPressed ? 0.95 : (_isDeliveryHovered ? 1.02 : 1.0)),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.orange.shade700,
                Colors.orange.shade800,
                Colors.orange.shade900,
              ],
            ),
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: const Color(0xFFB34700).withOpacity(0.45),
                blurRadius: _isDeliveryHovered ? 25 : 20,
                offset: Offset(0, _isDeliveryHovered ? 15 : 10),
                spreadRadius: _isDeliveryHovered ? 4 : 2,
              ),
              if (!isDark)
                BoxShadow(
                  color: const Color(0xFFB34700).withOpacity(0.18),
                  blurRadius: _isDeliveryHovered ? 20 : 15,
                  offset: Offset(0, _isDeliveryHovered ? 8 : 5),
                ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Align(
                alignment: Alignment.center,
                child: SizedBox(
                  height: 80,
                  width: 80,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.3),
                        width: 2,
                      ),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.delivery_dining_outlined,
                        size: 70,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 6),
              const Text(
                'DELIVERY',
                style: TextStyle(
                  fontSize: 35,
                  fontWeight: FontWeight.w900,
                  color: Colors.white,
                  letterSpacing: 1.5,
                  shadows: [
                    Shadow(
                      offset: Offset(0, 2),
                      blurRadius: 4,
                      color: Colors.black26,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  '${availableCities.length} CITIES AVAILABLE',
                  style: const TextStyle(
                    fontSize: 15,
                    color: Colors.white,
                    fontWeight: FontWeight.w700,
                    letterSpacing: 0.5,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

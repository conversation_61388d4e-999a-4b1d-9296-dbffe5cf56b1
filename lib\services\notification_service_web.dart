// 🌐 Web-Only Notification Service for Chica's Chicken App
// This service provides basic notification functionality for web platforms
// without Firebase dependencies

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/logger.dart';

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  // Cross-platform navigation callback
  Function(String)? _onNotificationTap;
  
  // Stream controllers for notification events
  final StreamController<String> _notificationTapController = StreamController<String>.broadcast();
  Stream<String> get onNotificationTap => _notificationTapController.stream;

  // 🚀 Initialize the notification service (web version)
  Future<void> initialize({Function(String)? onNotificationTap}) async {
    try {
      AppLogger.info('🔔 Initializing Web Notification Service...');
      
      _onNotificationTap = onNotificationTap;
      
      AppLogger.info('🌐 Web notification service initialized (limited functionality)');
    } catch (e) {
      AppLogger.error('❌ Failed to initialize Web Notification Service: $e');
    }
  }

  // 📅 Schedule daily feedback notification (web - no-op)
  Future<void> scheduleDailyFeedbackNotification() async {
    AppLogger.info('🌐 Notification scheduling not available on web platform');
  }

  // 🧪 Send a test notification (web - no-op)
  Future<void> sendTestNotification() async {
    AppLogger.info('🌐 Test notifications not available on web platform');
  }

  // ⚙️ User Preferences (Cross-platform)

  // Get daily feedback notification preference
  Future<bool> getDailyFeedbackEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool('daily_feedback_enabled') ?? true;
  }

  // Set daily feedback notification preference
  Future<void> setDailyFeedbackEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('daily_feedback_enabled', enabled);

    AppLogger.info('📱 Daily feedback notifications ${enabled ? 'enabled' : 'disabled'} (web - settings saved)');
  }

  // Check if notifications are enabled (for notification settings screen)
  Future<bool> areNotificationsEnabled() async {
    return await getDailyFeedbackEnabled();
  }

  // Set notifications enabled (for notification settings screen)
  Future<void> setNotificationsEnabled(bool enabled) async {
    await setDailyFeedbackEnabled(enabled);
  }

  // Stream getters for notification banner and test screen (web - empty streams)
  Stream<Map<String, dynamic>> get notificationStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  Stream<Map<String, dynamic>> get orderUpdateStream =>
      _notificationTapController.stream.map((route) => {'route': route});

  // WebSocket connection status (web - always false, no WebSocket)
  bool get isWebSocketConnected => false;

  // 🧹 Cleanup
  void dispose() {
    _notificationTapController.close();
  }
}

// 🔔 Notification Settings Screen
// This screen lets users control their notification preferences
// Think of it as the control panel for notifications!

import 'package:flutter/material.dart';
import '../services/notification_service.dart';

/// 🔔 Notification Settings Screen
/// Allows users to control their notification preferences
class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({Key? key}) : super(key: key);

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  final NotificationService _notificationService = NotificationService();
  bool _notificationsEnabled = true;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  /// 📊 Load current notification settings
  Future<void> _loadSettings() async {
    try {
      final enabled = await _notificationService.areNotificationsEnabled();
      setState(() {
        _notificationsEnabled = enabled;
        _isLoading = false;
      });
    } catch (error) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load settings');
    }
  }

  /// 💾 Save notification settings
  Future<void> _saveSettings(bool enabled) async {
    try {
      await _notificationService.setNotificationsEnabled(enabled);
      setState(() => _notificationsEnabled = enabled);
      
      _showSuccessSnackBar(enabled 
        ? 'Notifications enabled! You\'ll receive daily feedback reminders at 6 PM.'
        : 'Notifications disabled. You can re-enable them anytime.');
    } catch (error) {
      _showErrorSnackBar('Failed to save settings');
    }
  }

  /// 🧪 Send test notification
  Future<void> _sendTestNotification() async {
    try {
      await _notificationService.sendTestNotification();
      _showSuccessSnackBar('Test notification sent! Check your notification panel.');
    } catch (error) {
      _showErrorSnackBar('Failed to send test notification');
    }
  }

  /// ✅ Show success message
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// ❌ Show error message
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('🔔 Notification Settings'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section
                  _buildHeaderSection(),
                  const SizedBox(height: 30),
                  
                  // Main settings
                  _buildSettingsSection(),
                  const SizedBox(height: 30),
                  
                  // Test section
                  _buildTestSection(),
                  const SizedBox(height: 30),
                  
                  // Info section
                  _buildInfoSection(),
                ],
              ),
            ),
    );
  }

  /// 📋 Build header section
  Widget _buildHeaderSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(
            Icons.notifications_active,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          const Text(
            'Stay Connected!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Get friendly reminders to share feedback and earn rewards.',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  /// ⚙️ Build settings section
  Widget _buildSettingsSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Notification Preferences',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Daily feedback notifications toggle
            SwitchListTile(
              title: const Text(
                'Daily Feedback Reminders',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
              subtitle: const Text(
                'Receive a friendly reminder at 6 PM to share feedback and earn rewards',
              ),
              value: _notificationsEnabled,
              onChanged: _saveSettings,
              activeColor: Theme.of(context).primaryColor,
              secondary: const Icon(Icons.schedule),
            ),
          ],
        ),
      ),
    );
  }

  /// 🧪 Build test section
  Widget _buildTestSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Test Notifications',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'Want to see how notifications look? Send yourself a test notification!',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _sendTestNotification,
                icon: const Icon(Icons.send),
                label: const Text('Send Test Notification'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// ℹ️ Build info section
  Widget _buildInfoSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'How It Works',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoItem(
              Icons.schedule,
              'Daily Reminders',
              'Get a friendly notification every day at 6 PM',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              Icons.touch_app,
              'Tap to Navigate',
              'Tap the notification to go directly to your loyalty page',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              Icons.star,
              'Earn Rewards',
              'Share feedback to earn points and unlock rewards',
            ),
            const SizedBox(height: 12),
            _buildInfoItem(
              Icons.settings,
              'Full Control',
              'Enable or disable notifications anytime in settings',
            ),
          ],
        ),
      ),
    );
  }

  /// 📝 Build info item
  Widget _buildInfoItem(IconData icon, String title, String description) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          color: Theme.of(context).primaryColor,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(
                  color: Colors.grey,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

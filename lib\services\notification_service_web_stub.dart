// 🌐 Web Stub for Notification Service
// This file provides empty implementations for web compatibility

// Stub classes for Firebase
class Firebase {
  static List<dynamic> apps = [];
  static Future<void> initializeApp({dynamic options}) async {}
}

class FirebaseMessaging {
  static FirebaseMessaging get instance => FirebaseMessaging();
  Future<String?> getToken() async => null;
  Future<dynamic> requestPermission({
    bool? alert,
    bool? badge, 
    bool? sound,
    bool? provisional,
  }) async => null;
  Stream<dynamic> get onMessage => Stream.empty();
  Stream<dynamic> get onMessageOpenedApp => Stream.empty();
  Future<dynamic> getInitialMessage() async => null;
}

class DefaultFirebaseOptions {
  static dynamic get currentPlatform => null;
}

// Stub classes for Local Notifications
class FlutterLocalNotificationsPlugin {
  Future<void> initialize(dynamic settings, {dynamic onDidReceiveNotificationResponse}) async {}
  Future<void> cancel(int id) async {}
  Future<void> zonedSchedule(
    int id,
    String? title,
    String? body,
    dynamic scheduledDate,
    dynamic notificationDetails, {
    String? payload,
    dynamic androidScheduleMode,
    dynamic uiLocalNotificationDateInterpretation,
    dynamic matchDateTimeComponents,
  }) async {}
  Future<void> show(
    int id,
    String? title,
    String? body,
    dynamic notificationDetails, {
    String? payload,
  }) async {}
}

class AndroidInitializationSettings {
  const AndroidInitializationSettings(String icon);
}

class DarwinInitializationSettings {
  const DarwinInitializationSettings({
    bool? requestAlertPermission,
    bool? requestBadgePermission,
    bool? requestSoundPermission,
  });
}

class InitializationSettings {
  const InitializationSettings({
    dynamic android,
    dynamic iOS,
  });
}

class AndroidNotificationDetails {
  const AndroidNotificationDetails(
    String channelId,
    String channelName, {
    String? channelDescription,
    dynamic importance,
    dynamic priority,
    String? icon,
  });
}

class DarwinNotificationDetails {
  const DarwinNotificationDetails({
    bool? presentAlert,
    bool? presentBadge,
    bool? presentSound,
  });
}

class NotificationDetails {
  const NotificationDetails({
    dynamic android,
    dynamic iOS,
  });
}

// Stub enums
enum Importance { high }
enum Priority { high }
enum AndroidScheduleMode { exactAllowWhileIdle }
enum UILocalNotificationDateInterpretation { absoluteTime }
enum DateTimeComponents { time }

// Timezone stubs
class TZDateTime {
  static TZDateTime from(DateTime dateTime, dynamic location) => TZDateTime();
}

class TimeZone {
  static dynamic get local => null;
}

// Export stubs with proper names
final tz = TimeZone();
final tz_data = _TimeZoneData();

class _TimeZoneData {
  void initializeTimeZones() {}
}
